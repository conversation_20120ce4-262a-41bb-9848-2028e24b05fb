{"name": "workbox-expiration", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "A service worker helper library that expires cached responses based on age or maximum number of entries.", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "workbox-plugin"], "workbox": {"browserNamespace": "workbox.expiration", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"idb": "^7.0.1", "workbox-core": "6.6.0"}, "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}