'use strict';
require('../../modules/es.array.iterator');
require('../../modules/es.object.to-string');
require('../../modules/es.set');
require('../../modules/es.set.difference.v2');
require('../../modules/es.set.intersection.v2');
require('../../modules/es.set.is-disjoint-from.v2');
require('../../modules/es.set.is-subset-of.v2');
require('../../modules/es.set.is-superset-of.v2');
require('../../modules/es.set.symmetric-difference.v2');
require('../../modules/es.set.union.v2');
require('../../modules/es.string.iterator');
var path = require('../../internals/path');

module.exports = path.Set;
